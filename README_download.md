# MIMIC-CXR数据集下载工具

本工具用于将MIMIC-CXR数据集从Google Cloud Storage下载到本地项目目录。

## 前置要求

1. **安装Google Cloud SDK**
   ```bash
   # Ubuntu/Debian
   curl https://sdk.cloud.google.com | bash
   exec -l $SHELL
   
   # macOS (使用Homebrew)
   brew install google-cloud-sdk
   
   # 或者从官网下载: https://cloud.google.com/sdk/docs/install
   ```

2. **认证Google Cloud**
   ```bash
   gcloud auth login
   ```

3. **确保有访问MIMIC-CXR数据集的权限**
   - 需要在PhysioNet上完成CITI培训
   - 获得MIMIC-CXR数据集的访问权限

## 使用方法

### 方法1: 使用Python脚本（推荐）

```bash
python3 download_mimiccxr.py
```

Python脚本具有以下优势：
- 自动检查依赖项
- 更好的错误处理
- 详细的进度提示

### 方法2: 使用Shell脚本

```bash
./download_mimiccxr.sh
```

## 下载配置

- **源路径**: `gs://mimic-cxr-jpg-2.0.0.physionet.org/*`
- **目标路径**: `./data/MIMIC_CXR_JPG_FULL_DATASET/`
- **项目ID**: `commanding-way-467806-s4`

## 下载特性

- **多线程下载**: 使用`-m`参数加速下载
- **断点续传**: 使用`-n`参数，不会重复下载已存在的文件
- **递归下载**: 下载整个目录结构

## 注意事项

1. **存储空间**: MIMIC-CXR数据集约为470GB，请确保有足够的磁盘空间
2. **网络稳定**: 建议在网络稳定的环境下进行下载
3. **计费**: 下载会产生Google Cloud Storage的出站流量费用
4. **权限**: 确保您有访问该数据集的合法权限

## 目录结构

下载完成后，数据将保存在以下结构中：
```
./data/MIMIC_CXR_JPG_FULL_DATASET/
├── files/
│   ├── p10/
│   ├── p11/
│   └── ...
├── mimic-cxr-2.0.0-metadata.csv
├── mimic-cxr-2.0.0-split.csv
└── ...
```

## 故障排除

1. **认证问题**: 运行`gcloud auth login`重新认证
2. **权限问题**: 确认您有访问MIMIC-CXR数据集的权限
3. **网络问题**: 检查网络连接，必要时重新运行脚本（支持断点续传）
4. **空间不足**: 清理磁盘空间或更改目标路径

## 修改配置

如需修改下载路径或其他配置，请编辑脚本中的以下变量：
- `LOCAL_DESTINATION_PATH`: 本地目标路径
- `PROJECT_ID`: Google Cloud项目ID
- `GCS_SOURCE_PATH`: GCS源路径
