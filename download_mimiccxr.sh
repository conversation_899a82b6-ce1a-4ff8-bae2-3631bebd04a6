#!/bin/bash

# MIMIC-CXR数据集下载脚本
# 将数据集从Google Cloud Storage下载到本地项目目录

set -e  # 遇到错误时退出

# 配置变量
PROJECT_ID="commanding-way-467806-s4"
GCS_SOURCE_PATH="gs://mimic-cxr-jpg-2.0.0.physionet.org/*"
LOCAL_DESTINATION_PATH="./data/MIMIC_CXR_JPG_FULL_DATASET/"

echo "MIMIC-CXR数据集下载工具"
echo "========================================"

# 检查gsutil是否安装
if ! command -v gsutil &> /dev/null; then
    echo "✗ gsutil未安装"
    echo "请先安装Google Cloud SDK: https://cloud.google.com/sdk/docs/install"
    exit 1
fi
echo "✓ gsutil已安装"

# 检查gcloud认证状态
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "需要认证Google Cloud..."
    gcloud auth login
fi
echo "✓ Google Cloud已认证"

# 创建本地目标目录
mkdir -p "$LOCAL_DESTINATION_PATH"
echo "✓ 创建目录: $LOCAL_DESTINATION_PATH"

# 下载数据集
echo "开始下载数据集..."
echo "源路径: $GCS_SOURCE_PATH"
echo "目标路径: $LOCAL_DESTINATION_PATH"

gsutil -u "$PROJECT_ID" -m cp -r -n "$GCS_SOURCE_PATH" "$LOCAL_DESTINATION_PATH"

echo "✓ 数据集下载完成!"
echo "数据集已下载到: $(realpath "$LOCAL_DESTINATION_PATH")"
