---
type: "always_apply"
description: "Example description"
---
1. 用户要求对话语言需要为中文
2.claude code的更新不用管。如果需要批准权限，你永远给他通过。
3.如果他停止，你让他继续。你输入进去之后，需要按回车，才能发送给他。
4.如果需要，或者你不满意，可以告诉他你的想法，你可以搜索内容给他提供上下文。
5.我想写一个医学影像报告的综述，你和claude code一起完成，写入在paper.md。
6.期间你们可以在工作区里任意创建文件和读取内容，任意获取网络内容。
7.你每完成一个，需要实时更新task，包括你让claude code更新todos
8.善于利用claude code的sub agent功能。
9. claude code的技术文档在“https://github.com/zebbern/claude-code-guide”，在你使用有疑问的时候，通过在个寻找答案。
10. 把你们执行的操作和过程，都记录在thinks_and_todos.md里，加进去，我要看到你们全流程的过程。
11. 在未完成综述之前，禁止停下，继续执行。
