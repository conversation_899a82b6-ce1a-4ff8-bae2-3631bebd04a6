
#!/usr/bin/env python3
"""
下载MIMIC-CXR数据集到本地项目目录
需要先安装和配置Google Cloud SDK
"""

import os
import subprocess
import sys

# 项目配置
project_id = 'commanding-way-467806-s4'
gcs_source_path = 'gs://mimic-cxr-jpg-2.0.0.physionet.org/*'

# 本地目标路径 - 在当前项目目录下创建data文件夹
local_destination_path = './data/MIMIC_CXR_JPG_FULL_DATASET/'

def check_gsutil():
    """检查gsutil是否已安装"""
    try:
        subprocess.run(['gsutil', 'version'], capture_output=True, check=True)
        print("✓ gsutil已安装")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("✗ gsutil未安装或未配置")
        print("请先安装Google Cloud SDK: https://cloud.google.com/sdk/docs/install")
        return False

def authenticate_gcloud():
    """认证Google Cloud"""
    try:
        # 检查是否已经认证
        result = subprocess.run(['gcloud', 'auth', 'list'], capture_output=True, text=True)
        if 'ACTIVE' in result.stdout:
            print("✓ Google Cloud已认证")
            return True
        else:
            print("需要认证Google Cloud...")
            subprocess.run(['gcloud', 'auth', 'login'], check=True)
            return True
    except subprocess.CalledProcessError:
        print("✗ Google Cloud认证失败")
        return False

def create_local_directory():
    """创建本地目标目录"""
    os.makedirs(local_destination_path, exist_ok=True)
    print(f"✓ 创建目录: {local_destination_path}")

def download_dataset():
    """下载数据集"""
    print(f"开始下载数据集...")
    print(f"源路径: {gcs_source_path}")
    print(f"目标路径: {local_destination_path}")

    # 构建gsutil命令
    cmd = [
        'gsutil',
        '-u', project_id,  # 指定计费项目
        '-m',              # 多线程下载
        'cp',              # 复制命令
        '-r',              # 递归复制
        '-n',              # 不覆盖已存在的文件
        gcs_source_path,
        local_destination_path
    ]

    try:
        subprocess.run(cmd, check=True)
        print("✓ 数据集下载完成!")
    except subprocess.CalledProcessError as e:
        print(f"✗ 下载失败: {e}")
        return False

    return True

def main():
    """主函数"""
    print("MIMIC-CXR数据集下载工具")
    print("=" * 40)

    # 检查依赖
    if not check_gsutil():
        sys.exit(1)

    # 认证
    if not authenticate_gcloud():
        sys.exit(1)

    # 创建目录
    create_local_directory()

    # 下载数据集
    if download_dataset():
        print(f"\n数据集已下载到: {os.path.abspath(local_destination_path)}")
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()

